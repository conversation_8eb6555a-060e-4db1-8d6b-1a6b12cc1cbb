# 🔗 Estúdio730 - Link na Bio

Uma página web moderna e responsiva para funcionar como "link na bio" no Instagram de barbearias.

## 📱 Características

- **Design Responsivo**: Otimizado para dispositivos móveis
- **Tema Barbearia**: Cores escuras e masculinas com detalhes dourados
- **Animações Modernas**: Efeitos visuais suaves e profissionais
- **4 Botões Principais**: WhatsApp, Instagram, Localização e Site
- **Fácil Personalização**: Configurações centralizadas no JavaScript

## 🚀 Como Usar

1. **Abra o arquivo `script.js`**
2. **Edite a seção `LINKS_CONFIG`** com suas informações:

```javascript
const LINKS_CONFIG = {
    whatsapp: {
        phone: '5511999999999', // Seu número com código do país
        message: 'Sua mensagem personalizada'
    },
    instagram: {
        username: 'seu_instagram' // Seu username do Instagram
    },
    location: {
        address: 'Seu endereço completo'
    },
    website: {
        url: 'https://seu-site.com.br'
    }
};
```

3. **Personalize o nome da barbearia** no arquivo `index.html`:
   - Altere "Estúdio730" para o nome da sua barbearia
   - Modifique o subtítulo se desejar

## 📂 Estrutura dos Arquivos

```
link-na-bio/
├── index.html      # Estrutura HTML da página
├── styles.css      # Estilos e design responsivo
├── script.js       # Funcionalidades e configurações
└── README.md       # Este arquivo de instruções
```

## 🎨 Personalização Avançada

### Cores
No arquivo `styles.css`, você pode alterar as cores na seção `:root`:

```css
:root {
    --primary-color: #1a1a1a;      /* Cor de fundo principal */
    --secondary-color: #2d2d2d;    /* Cor dos botões */
    --accent-color: #d4af37;       /* Cor de destaque (dourado) */
    --text-light: #ffffff;         /* Texto claro */
    --text-gray: #cccccc;          /* Texto secundário */
}
```

### Nome e Subtítulo
No arquivo `index.html`, altere:

```html
<h1 class="title">SEU NOME AQUI</h1>
<p class="subtitle">Seu slogan aqui</p>
```

## 📱 Funcionalidades dos Botões

1. **WhatsApp**: Abre conversa direta com mensagem pré-definida
2. **Instagram**: Redireciona para o perfil do Instagram
3. **Localização**: Abre endereço no Google Maps
4. **Site**: Redireciona para site oficial

## 🔧 Recursos Técnicos

- **HTML5 Semântico**: Estrutura bem organizada
- **CSS3 Moderno**: Flexbox, Grid, Custom Properties
- **JavaScript ES6+**: Código limpo e modular
- **Font Awesome**: Ícones profissionais
- **Google Fonts**: Tipografia Poppins
- **Animações CSS**: Transições suaves
- **Console Logs**: Feedback para desenvolvimento

## 📊 Compatibilidade

- ✅ Chrome/Edge (versões recentes)
- ✅ Firefox (versões recentes)
- ✅ Safari (iOS/macOS)
- ✅ Dispositivos móveis (Android/iOS)
- ✅ Tablets e desktops

## 🚀 Como Publicar

### Opção 1: GitHub Pages (Gratuito)
1. Crie um repositório no GitHub
2. Faça upload dos arquivos
3. Ative GitHub Pages nas configurações
4. Use o link gerado no seu Instagram

### Opção 2: Netlify (Gratuito)
1. Acesse netlify.com
2. Arraste a pasta com os arquivos
3. Use o link gerado no seu Instagram

### Opção 3: Hospedagem Própria
1. Faça upload dos arquivos para seu servidor
2. Configure o domínio
3. Use o link no seu Instagram

## 📝 Dicas de Uso

1. **Teste sempre** em dispositivos móveis
2. **Mantenha os links atualizados**
3. **Use mensagens personalizadas** no WhatsApp
4. **Monitore os cliques** através do console do navegador
5. **Atualize regularmente** as informações

## 🎯 Exemplo de Configuração

```javascript
const LINKS_CONFIG = {
    whatsapp: {
        phone: '5511987654321',
        message: 'Olá! Vi seu Instagram e gostaria de agendar um corte.'
    },
    instagram: {
        username: 'estudio730'
    },
    location: {
        address: 'Rua Augusta, 1000, Consolação, São Paulo, SP'
    },
    website: {
        url: 'https://www.estudio730.com.br'
    }
};
```

## 📞 Suporte

Para dúvidas ou personalizações avançadas, consulte a documentação dos arquivos ou entre em contato.

---

**Desenvolvido com ❤️ para barbearias modernas**
