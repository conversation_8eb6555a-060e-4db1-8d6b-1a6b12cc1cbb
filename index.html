<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Estúdio730 - Links</title>
    <meta name="description" content="Todos os links do Estúdio730 em um só lugar">
    
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>✂️</text></svg>">
    
    <!-- CSS -->
    <link rel="stylesheet" href="styles.css">
    
    <!-- Font Awesome para ícones -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Botão de Configurações -->
    <button class="config-button" id="config-button" title="Configurações">
        <i class="fas fa-cog"></i>
    </button>

    <!-- Container principal -->
    <div class="container">
        <!-- Header com logo e nome da barbearia -->
        <header class="header">
            <div class="logo">
                <img src="logo.webp" alt="Estúdio730 Logo" class="logo-image">
            </div>
            <h1 class="title">Estúdio730</h1>
            <p class="subtitle">Estilo e tradição em cada corte</p>
        </header>

        <!-- Seção de links principais -->
        <main class="links-section">
            <!-- Botão WhatsApp -->
            <a href="#" class="link-button whatsapp" id="whatsapp-btn" target="_blank" rel="noopener noreferrer">
                <div class="button-content">
                    <i class="fab fa-whatsapp"></i>
                    <span class="button-text">
                        <strong>WhatsApp</strong>
                        <small>Agende seu horário</small>
                    </span>
                </div>
                <i class="fas fa-chevron-right arrow"></i>
            </a>

            <!-- Botão Instagram -->
            <a href="#" class="link-button instagram" id="instagram-btn" target="_blank" rel="noopener noreferrer">
                <div class="button-content">
                    <i class="fab fa-instagram"></i>
                    <span class="button-text">
                        <strong>Instagram</strong>
                        <small>Veja nossos trabalhos</small>
                    </span>
                </div>
                <i class="fas fa-chevron-right arrow"></i>
            </a>

            <!-- Botão Localização -->
            <a href="#" class="link-button location" id="location-btn" target="_blank" rel="noopener noreferrer">
                <div class="button-content">
                    <i class="fas fa-map-marker-alt"></i>
                    <span class="button-text">
                        <strong>Localização</strong>
                        <small>Como chegar</small>
                    </span>
                </div>
                <i class="fas fa-chevron-right arrow"></i>
            </a>

            <!-- Botão Site -->
            <a href="#" class="link-button website" id="website-btn" target="_blank" rel="noopener noreferrer">
                <div class="button-content">
                    <i class="fas fa-globe"></i>
                    <span class="button-text">
                        <strong>Site Oficial</strong>
                        <small>Conheça nossos serviços</small>
                    </span>
                </div>
                <i class="fas fa-chevron-right arrow"></i>
            </a>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <p>&copy; 2024 Estúdio730. Todos os direitos reservados.</p>
            <div class="social-icons">
                <i class="fab fa-instagram"></i>
                <i class="fab fa-facebook"></i>
                <i class="fab fa-whatsapp"></i>
            </div>
        </footer>
    </div>

    <!-- Modal de Configurações -->
    <div class="config-modal" id="config-modal">
        <div class="config-modal-content">
            <!-- Header do Modal -->
            <div class="config-header">
                <h2><i class="fas fa-cog"></i> Configurações</h2>
                <button class="config-close" id="config-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Conteúdo do Modal -->
            <div class="config-body">
                <!-- Seção de Links Existentes -->
                <div class="config-section">
                    <h3><i class="fas fa-link"></i> Gerenciar Links</h3>
                    <div class="links-list" id="links-list">
                        <!-- Links serão inseridos dinamicamente aqui -->
                    </div>
                </div>

                <!-- Seção Adicionar Novo Link -->
                <div class="config-section">
                    <h3><i class="fas fa-plus"></i> Adicionar Novo Link</h3>
                    <form class="add-link-form" id="add-link-form">
                        <div class="form-group">
                            <label for="link-name">Nome do Serviço *</label>
                            <input type="text" id="link-name" placeholder="Ex: Facebook, TikTok, Agendamento" required>
                        </div>

                        <div class="form-group">
                            <label for="link-url">URL do Link *</label>
                            <input type="url" id="link-url" placeholder="https://..." required>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="link-icon">Ícone Font Awesome</label>
                                <input type="text" id="link-icon" placeholder="fab fa-facebook">
                                <small>Ex: fab fa-facebook, fas fa-calendar</small>
                            </div>

                            <div class="form-group">
                                <label for="link-color">Cor do Botão</label>
                                <input type="color" id="link-color" value="#6c5ce7">
                            </div>
                        </div>

                        <button type="submit" class="btn-add-link">
                            <i class="fas fa-plus"></i> Adicionar Link
                        </button>
                    </form>
                </div>
            </div>

            <!-- Footer do Modal -->
            <div class="config-footer">
                <button class="btn-secondary" id="btn-restore">
                    <i class="fas fa-undo"></i> Restaurar Padrões
                </button>
                <div class="footer-actions">
                    <button class="btn-secondary" id="btn-cancel">Cancelar</button>
                    <button class="btn-primary" id="btn-save">
                        <i class="fas fa-save"></i> Salvar Configurações
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="script.js"></script>
    <script src="config-system.js"></script>
</body>
</html>
