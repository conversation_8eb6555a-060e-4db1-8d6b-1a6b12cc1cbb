/* Reset e configurações básicas */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Variáveis CSS para cores e espaçamentos */
:root {
    --primary-color: #0a0a0a;
    --secondary-color: #2d2d2d;
    --accent-color: #d4af37;
    --text-light: #ffffff;
    --text-gray: #cccccc;
    --whatsapp-color: #25d366;
    --instagram-color: #e4405f;
    --location-color: #4285f4;
    --website-color: #6c5ce7;
    --border-radius: 15px;
    --shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Configurações do body */
body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    min-height: 100vh;
    color: var(--text-light);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Container principal */
.container {
    max-width: 400px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 30px;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 20px;
}

.logo {
    width: 160px;
    height: 160px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    box-shadow: var(--shadow);
    animation: pulse 3s infinite;
    overflow: hidden;
}

.logo i {
    font-size: 2.5rem;
    color: var(--primary-color);
}

.logo-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    background: linear-gradient(135deg, var(--accent-color), #f1c40f);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.subtitle {
    font-size: 1rem;
    color: var(--text-gray);
    font-weight: 300;
}

/* Seção de links */
.links-section {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* Botões de link */
.link-button {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 25px;
    background: var(--secondary-color);
    border-radius: var(--border-radius);
    text-decoration: none;
    color: var(--text-light);
    transition: var(--transition);
    box-shadow: var(--shadow);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.link-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: var(--transition);
}

.link-button:hover::before {
    left: 100%;
}

.button-content {
    display: flex;
    align-items: center;
    gap: 20px;
}

.button-content i {
    font-size: 2rem;
    width: 50px;
    text-align: center;
}

.button-text {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.button-text strong {
    font-size: 1.1rem;
    font-weight: 600;
}

.button-text small {
    font-size: 0.85rem;
    color: var(--text-gray);
    font-weight: 300;
}

.arrow {
    font-size: 1.2rem;
    transition: var(--transition);
}

/* Efeitos hover específicos para cada botão */
.whatsapp:hover {
    transform: translateY(-5px);
    border-color: var(--whatsapp-color);
    box-shadow: 0 15px 40px rgba(37, 211, 102, 0.3);
}

.whatsapp .button-content i {
    color: var(--whatsapp-color);
}

.instagram:hover {
    transform: translateY(-5px);
    border-color: var(--instagram-color);
    box-shadow: 0 15px 40px rgba(228, 64, 95, 0.3);
}

.instagram .button-content i {
    color: var(--instagram-color);
}

.location:hover {
    transform: translateY(-5px);
    border-color: var(--location-color);
    box-shadow: 0 15px 40px rgba(66, 133, 244, 0.3);
}

.location .button-content i {
    color: var(--location-color);
}

.website:hover {
    transform: translateY(-5px);
    border-color: var(--website-color);
    box-shadow: 0 15px 40px rgba(108, 92, 231, 0.3);
}

.website .button-content i {
    color: var(--website-color);
}

.link-button:hover .arrow {
    transform: translateX(5px);
}

/* Footer */
.footer {
    text-align: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--secondary-color);
}

.footer p {
    font-size: 0.8rem;
    color: var(--text-gray);
    margin-bottom: 15px;
}

.social-icons {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.social-icons i {
    font-size: 1.5rem;
    color: var(--text-gray);
    transition: var(--transition);
    cursor: pointer;
}

.social-icons i:hover {
    color: var(--accent-color);
    transform: scale(1.2);
}

/* Animações */
@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.link-button {
    animation: fadeInUp 0.6s ease-out forwards;
}

.link-button:nth-child(1) { animation-delay: 0.1s; }
.link-button:nth-child(2) { animation-delay: 0.2s; }
.link-button:nth-child(3) { animation-delay: 0.3s; }
.link-button:nth-child(4) { animation-delay: 0.4s; }

/* Responsividade */
@media (max-width: 480px) {
    .container {
        padding: 15px;
        gap: 20px;
    }

    .logo {
        width: 100px;
        height: 100px;
        margin: 0 auto 20px;
    }

    .title {
        font-size: 2rem;
    }

    .link-button {
        padding: 18px 20px;
    }

    .button-content {
        gap: 15px;
    }

    .button-content i {
        font-size: 1.8rem;
        width: 45px;
    }
}

@media (max-width: 360px) {
    .container {
        padding: 10px;
    }

    .logo {
        width: 90px;
        height: 90px;
        margin: 0 auto 15px;
    }

    .title {
        font-size: 1.8rem;
    }

    .link-button {
        padding: 15px 18px;
    }
}
