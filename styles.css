/* Reset e configurações básicas */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Variáveis CSS para cores e espaçamentos */
:root {
    --primary-color: #0a0a0a;
    --secondary-color: #2d2d2d;
    --accent-color: #d4af37;
    --text-light: #ffffff;
    --text-gray: #cccccc;
    --whatsapp-color: #25d366;
    --instagram-color: #e4405f;
    --location-color: #4285f4;
    --website-color: #6c5ce7;
    --border-radius: 15px;
    --shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Configurações do body */
body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    min-height: 100vh;
    color: var(--text-light);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Container principal */
.container {
    max-width: 400px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 30px;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 20px;
}

.logo {
    width: 160px;
    height: 160px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    box-shadow: var(--shadow);
    animation: pulse 3s infinite;
    overflow: hidden;
}

.logo i {
    font-size: 2.5rem;
    color: var(--primary-color);
}

.logo-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    background: linear-gradient(135deg, var(--accent-color), #f1c40f);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.subtitle {
    font-size: 1rem;
    color: var(--text-gray);
    font-weight: 300;
}

/* Seção de links */
.links-section {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* Botões de link */
.link-button {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 25px;
    background: var(--secondary-color);
    border-radius: var(--border-radius);
    text-decoration: none;
    color: var(--text-light);
    transition: var(--transition);
    box-shadow: var(--shadow);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.link-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: var(--transition);
}

.link-button:hover::before {
    left: 100%;
}

.button-content {
    display: flex;
    align-items: center;
    gap: 20px;
}

.button-content i {
    font-size: 2rem;
    width: 50px;
    text-align: center;
}

.button-text {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.button-text strong {
    font-size: 1.1rem;
    font-weight: 600;
}

.button-text small {
    font-size: 0.85rem;
    color: var(--text-gray);
    font-weight: 300;
}

.arrow {
    font-size: 1.2rem;
    transition: var(--transition);
}

/* Efeitos hover específicos para cada botão */
.whatsapp:hover {
    transform: translateY(-5px);
    border-color: var(--whatsapp-color);
    box-shadow: 0 15px 40px rgba(37, 211, 102, 0.3);
}

.whatsapp .button-content i {
    color: var(--whatsapp-color);
}

.instagram:hover {
    transform: translateY(-5px);
    border-color: var(--instagram-color);
    box-shadow: 0 15px 40px rgba(228, 64, 95, 0.3);
}

.instagram .button-content i {
    color: var(--instagram-color);
}

.location:hover {
    transform: translateY(-5px);
    border-color: var(--location-color);
    box-shadow: 0 15px 40px rgba(66, 133, 244, 0.3);
}

.location .button-content i {
    color: var(--location-color);
}

.website:hover {
    transform: translateY(-5px);
    border-color: var(--website-color);
    box-shadow: 0 15px 40px rgba(108, 92, 231, 0.3);
}

.website .button-content i {
    color: var(--website-color);
}

.link-button:hover .arrow {
    transform: translateX(5px);
}

/* Links Personalizados */
.custom-link {
    border: 2px solid transparent;
}

.custom-link:hover {
    transform: translateY(-5px);
    border-color: var(--link-color, var(--accent-color));
}

.custom-link .button-content i {
    color: var(--link-color, var(--accent-color));
}

/* Footer */
.footer {
    text-align: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--secondary-color);
}

.footer p {
    font-size: 0.8rem;
    color: var(--text-gray);
    margin-bottom: 15px;
}

.social-icons {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.social-icons i {
    font-size: 1.5rem;
    color: var(--text-gray);
    transition: var(--transition);
    cursor: pointer;
}

.social-icons i:hover {
    color: var(--accent-color);
    transform: scale(1.2);
}

/* Animações */
@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.link-button {
    animation: fadeInUp 0.6s ease-out forwards;
}

.link-button:nth-child(1) { animation-delay: 0.1s; }
.link-button:nth-child(2) { animation-delay: 0.2s; }
.link-button:nth-child(3) { animation-delay: 0.3s; }
.link-button:nth-child(4) { animation-delay: 0.4s; }

/* Responsividade */
@media (max-width: 480px) {
    .container {
        padding: 15px;
        gap: 20px;
    }

    .logo {
        width: 100px;
        height: 100px;
        margin: 0 auto 20px;
    }

    .title {
        font-size: 2rem;
    }

    .link-button {
        padding: 18px 20px;
    }

    .button-content {
        gap: 15px;
    }

    .button-content i {
        font-size: 1.8rem;
        width: 45px;
    }
}

@media (max-width: 360px) {
    .container {
        padding: 10px;
    }

    .logo {
        width: 90px;
        height: 90px;
        margin: 0 auto 15px;
    }

    .title {
        font-size: 1.8rem;
    }

    .link-button {
        padding: 15px 18px;
    }
}

/* ===== SISTEMA DE CONFIGURAÇÕES ===== */

/* Botão de Configurações */
.config-button {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    background: var(--secondary-color);
    border: 2px solid transparent;
    border-radius: 50%;
    color: var(--text-light);
    font-size: 1.2rem;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.config-button:hover {
    background: var(--accent-color);
    color: var(--primary-color);
    transform: rotate(90deg) scale(1.1);
    border-color: var(--accent-color);
}

/* Modal de Configurações */
.config-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    z-index: 2000;
    overflow-y: auto;
    padding: 20px;
}

.config-modal.active {
    display: flex;
    align-items: flex-start;
    justify-content: center;
    animation: fadeIn 0.3s ease-out;
}

.config-modal-content {
    background: var(--primary-color);
    border-radius: var(--border-radius);
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    animation: slideUp 0.3s ease-out;
    margin-top: 20px;
}

/* Header do Modal */
.config-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 25px 30px;
    border-bottom: 1px solid var(--secondary-color);
    background: linear-gradient(135deg, var(--secondary-color), #1f1f1f);
}

.config-header h2 {
    color: var(--accent-color);
    font-size: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.config-close {
    background: none;
    border: none;
    color: var(--text-gray);
    font-size: 1.5rem;
    cursor: pointer;
    transition: var(--transition);
    padding: 5px;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.config-close:hover {
    color: var(--text-light);
    background: rgba(255, 255, 255, 0.1);
}

/* Corpo do Modal */
.config-body {
    padding: 30px;
    max-height: 60vh;
    overflow-y: auto;
}

.config-section {
    margin-bottom: 40px;
}

.config-section h3 {
    color: var(--accent-color);
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--secondary-color);
}

/* Lista de Links */
.links-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.link-item {
    background: var(--secondary-color);
    border-radius: 10px;
    padding: 20px;
    border: 1px solid transparent;
    transition: var(--transition);
}

.link-item:hover {
    border-color: var(--accent-color);
}

.link-item-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
}

.link-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.link-preview {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
}

.link-details h4 {
    color: var(--text-light);
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 2px;
}

.link-details small {
    color: var(--text-gray);
    font-size: 0.85rem;
}

.link-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Toggle Switch */
.toggle-switch {
    position: relative;
    width: 50px;
    height: 25px;
    background: #333;
    border-radius: 25px;
    cursor: pointer;
    transition: var(--transition);
}

.toggle-switch.active {
    background: var(--accent-color);
}

.toggle-switch::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 21px;
    height: 21px;
    background: white;
    border-radius: 50%;
    transition: var(--transition);
}

.toggle-switch.active::after {
    transform: translateX(25px);
}

/* Botões de Controle */
.control-btn {
    background: none;
    border: 1px solid var(--text-gray);
    color: var(--text-gray);
    width: 30px;
    height: 30px;
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
}

.control-btn:hover {
    border-color: var(--accent-color);
    color: var(--accent-color);
}

.control-btn.danger:hover {
    border-color: #e74c3c;
    color: #e74c3c;
}

.control-btn.edit:hover {
    border-color: var(--accent-color);
    color: var(--accent-color);
}

/* Botão Expandir Formulário */
.btn-expand-form {
    background: var(--accent-color);
    color: var(--primary-color);
    border: none;
    padding: 15px 25px;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1rem;
    width: 100%;
    justify-content: center;
    margin-bottom: 20px;
}

.btn-expand-form:hover {
    background: #f1c40f;
    transform: translateY(-2px);
}

.btn-expand-form.hidden {
    display: none;
}

/* Container do Formulário Expansível */
.add-link-form-container {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.add-link-form-container.expanded {
    max-height: 600px;
}

/* Formulários */
.add-link-form {
    background: var(--secondary-color);
    padding: 25px;
    border-radius: 10px;
    border: 1px solid #333;
    margin-top: 0;
    opacity: 0;
    transform: translateY(-20px);
    transition: all 0.3s ease-out 0.1s;
}

.add-link-form-container.expanded .add-link-form {
    opacity: 1;
    transform: translateY(0);
}

.form-group {
    margin-bottom: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 120px;
    gap: 15px;
}

.form-group label {
    display: block;
    color: var(--text-light);
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.form-group input {
    width: 100%;
    padding: 12px 15px;
    background: var(--primary-color);
    border: 1px solid #333;
    border-radius: 8px;
    color: var(--text-light);
    font-size: 0.9rem;
    transition: var(--transition);
}

.form-group input:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
}

.form-group small {
    color: var(--text-gray);
    font-size: 0.8rem;
    margin-top: 5px;
    display: block;
}

.form-group input[type="color"] {
    height: 45px;
    padding: 5px;
    cursor: pointer;
}

/* Ações do Formulário */
.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 20px;
}

.btn-cancel-form {
    background: transparent;
    color: var(--text-gray);
    border: 1px solid var(--text-gray);
    padding: 12px 20px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.btn-cancel-form:hover {
    color: var(--text-light);
    border-color: var(--text-light);
}

/* Botões */
.btn-add-link {
    background: var(--accent-color);
    color: var(--primary-color);
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.btn-add-link:hover {
    background: #f1c40f;
    transform: translateY(-2px);
}

/* Footer do Modal */
.config-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 25px 30px;
    border-top: 1px solid var(--secondary-color);
    background: #1a1a1a;
}

.footer-actions {
    display: flex;
    gap: 15px;
}

.btn-primary, .btn-secondary {
    padding: 12px 20px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    border: none;
}

.btn-primary {
    background: var(--accent-color);
    color: var(--primary-color);
}

.btn-primary:hover {
    background: #f1c40f;
    transform: translateY(-2px);
}

.btn-secondary {
    background: transparent;
    color: var(--text-gray);
    border: 1px solid var(--text-gray);
}

.btn-secondary:hover {
    color: var(--text-light);
    border-color: var(--text-light);
}

/* Animações */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsividade do Modal */
@media (max-width: 768px) {
    .config-modal {
        padding: 10px;
    }

    .config-modal-content {
        margin-top: 10px;
    }

    .config-header {
        padding: 20px;
    }

    .config-body {
        padding: 20px;
    }

    .config-footer {
        padding: 20px;
        flex-direction: column;
        gap: 15px;
    }

    .footer-actions {
        width: 100%;
        justify-content: space-between;
    }

    .form-row {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .config-button {
        top: 15px;
        right: 15px;
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }

    .link-item {
        padding: 15px;
    }

    .link-item-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .link-controls {
        align-self: stretch;
        justify-content: space-between;
    }
}

/* ===== MODAL DE EDIÇÃO ===== */

/* Modal de Edição */
.edit-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    z-index: 2500;
    overflow-y: auto;
    padding: 20px;
}

.edit-modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease-out;
}

.edit-modal-content {
    background: var(--primary-color);
    border-radius: var(--border-radius);
    width: 100%;
    max-width: 500px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    animation: slideUp 0.3s ease-out;
}

/* Header do Modal de Edição */
.edit-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 25px;
    border-bottom: 1px solid var(--secondary-color);
    background: linear-gradient(135deg, var(--secondary-color), #1f1f1f);
}

.edit-header h3 {
    color: var(--accent-color);
    font-size: 1.3rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
}

.edit-close {
    background: none;
    border: none;
    color: var(--text-gray);
    font-size: 1.3rem;
    cursor: pointer;
    transition: var(--transition);
    padding: 5px;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.edit-close:hover {
    color: var(--text-light);
    background: rgba(255, 255, 255, 0.1);
}

/* Corpo do Modal de Edição */
.edit-body {
    padding: 25px;
}

.edit-link-form {
    background: var(--secondary-color);
    padding: 20px;
    border-radius: 10px;
    border: 1px solid #333;
}

/* Ações do Modal de Edição */
.edit-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 20px;
}

/* Responsividade do Modal de Edição */
@media (max-width: 768px) {
    .edit-modal {
        padding: 10px;
    }

    .edit-modal-content {
        max-width: 100%;
    }

    .edit-header {
        padding: 15px 20px;
    }

    .edit-body {
        padding: 20px;
    }

    .edit-actions {
        flex-direction: column;
    }
}
